<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soulweave改衣坊 - 充值记录管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fff;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border-radius: 8px;
            flex-wrap: wrap;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .staff-info {
            display: flex;
            align-items: center;
            gap: 15px;
            background: #f5f5f5;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .logout-btn {
            color: #dc3545;
            text-decoration: none;
            font-weight: 500;
        }
        .logout-btn:hover {
            text-decoration: underline;
        }
        h1 {
            font-size: 1.8rem;
            margin: 0;
        }
        a {
            text-decoration: none;
            color: #007BFF;
        }
        a:hover {
            text-decoration: underline;
        }
        .search-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .search-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2rem;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 15px;
            background: #007BFF;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 4px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-sm {
            padding: 5px 10px;
            font-size: 0.875rem;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            .header-controls {
                flex-direction: column;
                gap: 10px;
            }
            .staff-info {
                flex-direction: column;
                gap: 5px;
            }
            h1 {
                font-size: 1.5rem;
            }
            .search-section {
                padding: 15px;
            }
            .search-section h3 {
                font-size: 1.1rem;
            }
            input, select, textarea, button {
                font-size: 16px; /* 防止iOS缩放 */
            }
            table {
                font-size: 12px;
            }
            th, td {
                padding: 6px !important;
            }
            .btn-sm {
                padding: 3px 6px;
                font-size: 0.75rem;
            }
            /* 模态框移动端适配 */
            #refundModal > div, #reprintModal > div, #refundHistoryModal > div {
                width: 95% !important;
                max-width: none !important;
                margin: 10px !important;
                max-height: 90vh !important;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="header">
        <h1>充值记录管理</h1>
        <div class="header-controls">
            <div class="staff-info">
                当前用户: <span id="staffName">{{ session.staff_name or '未登录' }}</span>
                <span>|</span>
                角色: <span id="staffRole">{{ session.staff_role or '未知' }}</span>
            </div>
            <a href="/">返回首页</a>
            <a href="/logout" class="logout-btn">登出</a>
        </div>
    </div>

    <div class="container">
        <!-- 搜索筛选区域 -->
        <div class="search-section">
            <h3>搜索筛选</h3>
            <form id="searchForm">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                    <div class="form-group">
                        <label>客户姓名</label>
                        <input type="text" id="customerName" placeholder="输入客户姓名">
                    </div>
                    <div class="form-group">
                        <label>客户手机</label>
                        <input type="text" id="customerPhone" placeholder="输入手机号">
                    </div>
                    <div class="form-group">
                        <label>开始日期</label>
                        <input type="date" id="startDate">
                    </div>
                    <div class="form-group">
                        <label>结束日期</label>
                        <input type="date" id="endDate">
                    </div>
                    <div class="form-group">
                        <label>操作员</label>
                        <input type="text" id="operator" placeholder="操作员">
                    </div>
                </div>
                <div>
                    <button type="button" onclick="searchRechargeRecords()">搜索</button>
                    <button type="button" class="btn-secondary" onclick="resetSearch()">重置</button>
                    <button type="button" class="btn-success" onclick="exportRecords()">导出</button>
                    <button type="button" class="btn-secondary" onclick="location.reload()">刷新</button>
                </div>
            </form>
        </div>
        <!-- 充值记录表格 -->
        <div class="search-section">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3>充值记录</h3>
                <div>
                    <span style="background: #007BFF; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.9em;" id="totalCount">总计: 0 条</span>
                </div>
            </div>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); font-size: 14px;">
                    <thead>
                        <tr style="background-color: #f2f7ff;">
                            <th style="padding: 10px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold; color: #333;">ID</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold; color: #333;">客户信息</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold; color: #333;">充值金额</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold; color: #333;">赠送金额</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold; color: #333;">支付方式</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold; color: #333;">充值时间</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold; color: #333;">操作员</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold; color: #333;">状态</th>
                            <th style="padding: 10px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold; color: #333;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="rechargeTableBody">
                        <tr>
                            <td colspan="9" style="padding: 40px; text-align: center; border: 1px solid #e0e0e0;">
                                <div style="color: #007BFF;">正在加载数据...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div id="paginationNav" style="display: none; text-align: center; margin-top: 20px;">
                <div id="pagination" style="display: inline-flex; gap: 5px;">
                </div>
            </div>
        </div>
    </div>

    <!-- 退充确认模态框 -->
    <div id="refundModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; padding: 20px; max-width: 500px; width: 90%;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                <h3 style="margin: 0; color: #dc3545;">充值退充</h3>
                <button type="button" onclick="closeModal('refundModal')" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
            </div>
            <div id="refundInfo" style="margin-bottom: 20px;">
                <!-- 退充信息将在这里显示 -->
            </div>
            <form id="refundForm">
                <input type="hidden" id="refundRechargeId">
                <div class="form-group">
                    <label>退充金额 <span style="color: #dc3545;">*</span></label>
                    <div style="display: flex; align-items: center;">
                        <span style="padding: 8px 12px; background: #f8f9fa; border: 1px solid #ddd; border-right: none; border-radius: 4px 0 0 4px;">¥</span>
                        <input type="number" id="refundAmount" step="0.01" min="0" required style="border-radius: 0 4px 4px 0; border-left: none;">
                    </div>
                    <small style="color: #6c757d;">最大可退充金额: ¥<span id="maxRefundAmount">0.00</span></small>
                </div>
                <div class="form-group">
                    <label>退充原因 <span style="color: #dc3545;">*</span></label>
                    <textarea id="refundReason" rows="3" placeholder="请输入退充原因" required></textarea>
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="printRefundReceipt" checked style="margin-right: 8px;">
                        自动打印退充小票
                    </label>
                </div>
            </form>
            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                <button type="button" class="btn-secondary" onclick="closeModal('refundModal')">取消</button>
                <button type="button" class="btn-danger" id="confirmRefundBtn" onclick="executeRefund()">确认退充</button>
            </div>
        </div>
    </div>
    <!-- 补打印确认模态框 -->
    <div id="reprintModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; padding: 20px; max-width: 500px; width: 90%;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                <h3 style="margin: 0; color: #28a745;">小票补打印</h3>
                <button type="button" onclick="closeModal('reprintModal')" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
            </div>
            <div id="reprintInfo" style="margin-bottom: 20px;">
                <!-- 补打印信息将在这里显示 -->
            </div>
            <form id="reprintForm">
                <input type="hidden" id="reprintRecordId">
                <input type="hidden" id="reprintRecordType">
                <div class="form-group">
                    <label>补打印原因</label>
                    <input type="text" id="reprintReason" placeholder="请输入补打印原因" value="客户要求补打印">
                </div>
            </form>
            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                <button type="button" class="btn-secondary" onclick="closeModal('reprintModal')">取消</button>
                <button type="button" class="btn-success" id="confirmReprintBtn" onclick="executeReprint()">确认补打印</button>
            </div>
        </div>
    </div>

    <!-- 退充记录查看模态框 -->
    <div id="refundHistoryModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; padding: 20px; max-width: 800px; width: 90%; max-height: 80vh; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px;">
                <h3 style="margin: 0;">退充记录</h3>
                <button type="button" onclick="closeModal('refundHistoryModal')" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
            </div>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <thead>
                        <tr style="background-color: #f2f7ff;">
                            <th style="padding: 8px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold;">退充时间</th>
                            <th style="padding: 8px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold;">退充金额</th>
                            <th style="padding: 8px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold;">退充原因</th>
                            <th style="padding: 8px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold;">操作员</th>
                            <th style="padding: 8px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold;">状态</th>
                            <th style="padding: 8px; text-align: left; border: 1px solid #e0e0e0; font-weight: bold;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="refundHistoryTableBody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script language="javascript" src="/static/js/LodopFuncs.js"></script>
    <script src="/static/js/lodop-print.js"></script>
    <script src="/static/js/refund-print.js"></script>
    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let currentSearchParams = {};

        // 模态框控制函数
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期范围（最近7天）
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 7);

            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];

            // 加载充值记录
            searchRechargeRecords();
        });

        // 搜索充值记录
        async function searchRechargeRecords(page = 1) {
            try {
                currentPage = page;
                
                // 构建搜索参数
                const params = new URLSearchParams({
                    page: page,
                    per_page: 20
                });
                
                const customerName = document.getElementById('customerName').value.trim();
                const customerPhone = document.getElementById('customerPhone').value.trim();
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                const operator = document.getElementById('operator').value.trim();
                
                if (customerName) params.append('customer_name', customerName);
                if (customerPhone) params.append('customer_phone', customerPhone);
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                if (operator) params.append('operator', operator);
                
                currentSearchParams = Object.fromEntries(params);
                
                // 显示加载状态
                showLoading();
                
                const response = await fetch(`/api/recharge_details?${params}`);
                const result = await response.json();
                
                if (result.success) {
                    displayRechargeRecords(result.recharge_details);
                    updatePagination(result.current_page, result.total_pages, result.total);
                } else {
                    showError('加载充值记录失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 显示充值记录
        function displayRechargeRecords(records) {
            const tbody = document.getElementById('rechargeTableBody');

            if (!records || records.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="padding: 40px; text-align: center; border: 1px solid #e0e0e0; color: #6c757d;">
                            暂无充值记录
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = records.map(record => `
                <tr style="border-bottom: 1px solid #e0e0e0;">
                    <td style="padding: 10px; border: 1px solid #e0e0e0;">${record.id}</td>
                    <td style="padding: 10px; border: 1px solid #e0e0e0;">
                        <div><strong>${record.customer_name}</strong></div>
                        <small style="color: #6c757d;">${record.customer_phone}</small>
                    </td>
                    <td style="padding: 10px; border: 1px solid #e0e0e0;"><span style="font-weight: bold; color: #28a745;">¥${record.amount.toFixed(2)}</span></td>
                    <td style="padding: 10px; border: 1px solid #e0e0e0;"><span style="color: #28a745;">¥${record.gift_amount.toFixed(2)}</span></td>
                    <td style="padding: 10px; border: 1px solid #e0e0e0;"><span style="background: #007BFF; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">${record.payment_method}</span></td>
                    <td style="padding: 10px; border: 1px solid #e0e0e0;"><small>${new Date(record.created_at).toLocaleString()}</small></td>
                    <td style="padding: 10px; border: 1px solid #e0e0e0;"><span style="background: #6c757d; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">${record.operator}</span></td>
                    <td style="padding: 10px; border: 1px solid #e0e0e0;">
                        ${getRefundStatusBadge(record.refund_status)}
                    </td>
                    <td style="padding: 10px; border: 1px solid #e0e0e0;">
                        <div style="display: flex; gap: 5px;">
                            <button class="btn-danger btn-sm"
                                    onclick="${record.refund_status === '已退' ? '' : `showRefundModal(${record.id})`}"
                                    title="${record.refund_status === '已退' ? '已全额退充' : '退充'}"
                                    ${record.refund_status === '已退' ? 'disabled style="opacity: 0.5; cursor: not-allowed;"' : ''}>
                                退充
                            </button>
                            <button class="btn-success btn-sm" onclick="showReprintModal(${record.id}, 'recharge')"
                                    title="补打印">
                                补打印
                            </button>
                            <button class="btn-sm" onclick="showRefundHistory(${record.id})"
                                    title="退充记录" style="background: #17a2b8; color: white;">
                                记录
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 获取退充状态徽章
        function getRefundStatusBadge(status) {
            if (status === '已退') {
                return '<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">已退</span>';
            } else if (status.startsWith('部分退充')) {
                return '<span style="background: #ffc107; color: #212529; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">部分退充</span>';
            } else {
                return '<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">正常</span>';
            }
        }

        // 显示退充模态框
        async function showRefundModal(rechargeId) {
            try {
                // 获取可退充信息
                const response = await fetch(`/api/recharge/${rechargeId}/refundable`);
                const result = await response.json();
                
                if (!result.success) {
                    showAlert('获取退充信息失败: ' + result.error, 'danger');
                    return;
                }
                
                // 填充退充信息
                document.getElementById('refundRechargeId').value = rechargeId;
                document.getElementById('maxRefundAmount').textContent = result.refundable_amount.toFixed(2);
                document.getElementById('refundAmount').max = result.refundable_amount;

                const refundInfo = document.getElementById('refundInfo');
                refundInfo.innerHTML = `
                    <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 15px;">
                        <h4 style="margin: 0 0 10px 0; color: #0c5460;">退充信息</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <small style="color: #6c757d;">可退充金额:</small><br>
                                <strong style="color: #28a745;">¥${result.refundable_amount.toFixed(2)}</strong>
                            </div>
                            <div>
                                <small style="color: #6c757d;">当前充值余额:</small><br>
                                <strong>¥${result.current_balance.toFixed(2)}</strong>
                            </div>
                        </div>
                        ${result.current_gift_balance > 0 ? `
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 10px;">
                                <div>
                                    <small style="color: #6c757d;">当前赠送余额:</small><br>
                                    <strong style="color: #17a2b8;">¥${result.current_gift_balance.toFixed(2)}</strong>
                                </div>
                                <div>
                                    <small style="color: #6c757d;">总余额:</small><br>
                                    <strong style="color: #007BFF;">¥${(result.current_balance + result.current_gift_balance).toFixed(2)}</strong>
                                </div>
                            </div>
                        ` : ''}
                        ${result.gift_amount_to_deduct > 0 ? `
                            <div style="margin-top: 10px;">
                                <small style="color: #6c757d;">将扣除赠送金额: </small>
                                <span style="color: #ffc107;">¥${result.gift_amount_to_deduct.toFixed(2)}</span>
                            </div>
                        ` : ''}
                        ${!result.can_refund ? `
                            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin-top: 10px;">
                                <small style="color: #856404;">${result.reason}</small>
                            </div>
                        ` : ''}
                    </div>
                `;

                // 重置表单
                document.getElementById('refundForm').reset();
                document.getElementById('refundRechargeId').value = rechargeId;

                // 显示模态框
                showModal('refundModal');
                
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'danger');
            }
        }

        // 执行退充操作
        async function executeRefund() {
            try {
                const rechargeId = document.getElementById('refundRechargeId').value;
                const refundAmount = parseFloat(document.getElementById('refundAmount').value);
                const refundReason = document.getElementById('refundReason').value.trim();
                const printReceipt = document.getElementById('printRefundReceipt').checked;
                
                if (!refundAmount || refundAmount <= 0) {
                    showAlert('请输入有效的退充金额', 'warning');
                    return;
                }
                
                if (!refundReason) {
                    showAlert('请输入退充原因', 'warning');
                    return;
                }
                
                // 禁用按钮，显示加载状态
                const btn = document.getElementById('confirmRefundBtn');
                const originalText = btn.innerHTML;
                btn.disabled = true;
                btn.innerHTML = '处理中...';

                const response = await fetch(`/api/recharge/${rechargeId}/refund`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        refund_amount: refundAmount,
                        refund_reason: refundReason,
                        print_receipt: printReceipt
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`退充成功！退充ID: ${result.refund_id}`, 'success');
                    
                    // 关闭模态框
                    closeModal('refundModal');
                    
                    // 刷新列表
                    searchRechargeRecords(currentPage);
                } else {
                    showAlert('退充失败: ' + result.error, 'danger');
                }
                
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'danger');
            } finally {
                // 恢复按钮状态
                const btn = document.getElementById('confirmRefundBtn');
                btn.disabled = false;
                btn.innerHTML = '确认退充';
            }
        }     
   // 显示补打印模态框
        function showReprintModal(recordId, recordType) {
            document.getElementById('reprintRecordId').value = recordId;
            document.getElementById('reprintRecordType').value = recordType;
            
            const reprintInfo = document.getElementById('reprintInfo');
            reprintInfo.innerHTML = `
                <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 15px;">
                    <h4 style="margin: 0 0 10px 0; color: #0c5460;">补打印信息</h4>
                    <p style="margin: 0;">
                        记录类型: <span style="background: #007BFF; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">${recordType === 'recharge' ? '充值小票' : '退充小票'}</span><br>
                        记录ID: <strong>${recordId}</strong>
                    </p>
                </div>
            `;

            // 重置表单
            document.getElementById('reprintForm').reset();
            document.getElementById('reprintReason').value = '客户要求补打印';
            document.getElementById('reprintRecordId').value = recordId;
            document.getElementById('reprintRecordType').value = recordType;

            // 显示模态框
            showModal('reprintModal');
        }

        // 执行补打印操作
        async function executeReprint() {
            try {
                const recordId = document.getElementById('reprintRecordId').value;
                const recordType = document.getElementById('reprintRecordType').value;
                const reprintReason = document.getElementById('reprintReason').value.trim();
                
                // 禁用按钮，显示加载状态
                const btn = document.getElementById('confirmReprintBtn');
                const originalText = btn.innerHTML;
                btn.disabled = true;
                btn.innerHTML = '处理中...';

                let apiUrl;
                if (recordType === 'recharge') {
                    apiUrl = `/api/recharge/${recordId}/reprint`;
                } else {
                    apiUrl = `/api/recharge/refund/${recordId}/reprint`;
                }
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        reprint_reason: reprintReason
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`补打印成功！记录ID: ${result.reprint_id}`, 'success');
                    
                    // 关闭模态框
                    closeModal('reprintModal');
                    
                    // 调用打印功能 - 使用新的模式选择接口
                    if (recordType === 'recharge') {
                        try {
                            // 优先使用新的模式选择接口
                            if (typeof window.reprintRechargeReceiptWithModeSelection === 'function') {
                                console.log('使用新的充值小票补打印接口');
                                window.reprintRechargeReceiptWithModeSelection(recordId, reprintReason);
                            } else if (typeof window.reprintRechargeReceipt === 'function') {
                                console.log('使用兼容的充值小票补打印接口');
                                window.reprintRechargeReceipt(recordId, reprintReason);
                            } else {
                                throw new Error('充值小票补打印函数不可用');
                            }
                        } catch (printError) {
                            console.error('调用充值补打印函数失败:', printError);
                            showAlert('充值小票打印失败: ' + printError.message, 'warning');
                        }
                    } else if (recordType === 'refund') {
                        try {
                            // 优先使用新的模式选择接口
                            if (typeof window.reprintRefundReceiptWithModeSelection === 'function') {
                                console.log('使用新的退充小票补打印接口');
                                window.reprintRefundReceiptWithModeSelection(recordId, reprintReason);
                            } else if (typeof window.reprintRefundReceipt === 'function') {
                                console.log('使用兼容的退充小票补打印接口');
                                window.reprintRefundReceipt(recordId, reprintReason);
                            } else {
                                throw new Error('退充小票补打印函数不可用');
                            }
                        } catch (printError) {
                            console.error('调用退充补打印函数失败:', printError);
                            showAlert('退充小票打印失败: ' + printError.message, 'warning');
                        }
                    } else {
                        console.error('未知的补打印类型:', recordType);
                        showAlert('未知的补打印类型，请检查参数', 'warning');
                    }
                } else {
                    showAlert('补打印失败: ' + result.error, 'danger');
                }
                
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'danger');
            } finally {
                // 恢复按钮状态
                const btn = document.getElementById('confirmReprintBtn');
                btn.disabled = false;
                btn.innerHTML = '确认补打印';
            }
        }

        // 显示退充记录
        async function showRefundHistory(rechargeId) {
            try {
                const response = await fetch(`/api/recharge/refunds?original_recharge_id=${rechargeId}`);
                const result = await response.json();
                
                const tbody = document.getElementById('refundHistoryTableBody');
                
                if (result.success && result.refunds.length > 0) {
                    tbody.innerHTML = result.refunds.map(refund => `
                        <tr>
                            <td><small>${new Date(refund.created_at).toLocaleString()}</small></td>
                            <td><span class="refund-amount">¥${refund.refund_amount.toFixed(2)}</span></td>
                            <td><small>${refund.refund_reason || '无'}</small></td>
                            <td><span style="background: #6c757d; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">${refund.operator}</span></td>
                            <td><span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">${refund.status}</span></td>
                            <td>
                                <button class="btn-success btn-sm"
                                        onclick="showReprintModal(${refund.id}, 'refund')" title="补打印退充小票">
                                    补打印
                                </button>
                            </td>
                        </tr>
                    `).join('');
                } else {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="6" style="padding: 20px; text-align: center; color: #6c757d; border: 1px solid #e0e0e0;">
                                暂无退充记录
                            </td>
                        </tr>
                    `;
                }
                
                // 显示模态框
                showModal('refundHistoryModal');
                
            } catch (error) {
                showAlert('获取退充记录失败: ' + error.message, 'danger');
            }
        }  
        // 更新分页
        function updatePagination(currentPage, totalPages, totalCount) {
            document.getElementById('totalCount').textContent = `总计: ${totalCount} 条`;

            const paginationNav = document.getElementById('paginationNav');
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                paginationNav.style.display = 'none';
                return;
            }

            paginationNav.style.display = 'block';

            let paginationHTML = '';

            // 上一页
            if (currentPage > 1) {
                paginationHTML += `
                    <button onclick="searchRechargeRecords(${currentPage - 1})" style="margin: 0 2px;">上一页</button>
                `;
            }

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                paginationHTML += `
                    <button onclick="searchRechargeRecords(${i})"
                            style="margin: 0 2px; ${isActive ? 'background: #0056b3; color: white;' : ''}">${i}</button>
                `;
            }

            // 下一页
            if (currentPage < totalPages) {
                paginationHTML += `
                    <button onclick="searchRechargeRecords(${currentPage + 1})" style="margin: 0 2px;">下一页</button>
                `;
            }

            pagination.innerHTML = paginationHTML;
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchForm').reset();
            
            // 重新设置默认日期
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 7);
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            
            // 重新搜索
            searchRechargeRecords(1);
        }

        // 导出记录
        function exportRecords() {
            const params = new URLSearchParams(currentSearchParams);
            params.delete('page');
            params.delete('per_page');
            
            const url = `/api/export_recharge_details?${params}`;
            window.open(url, '_blank');
        }

        // 显示加载状态
        function showLoading() {
            const tbody = document.getElementById('rechargeTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载数据...</p>
                    </td>
                </tr>
            `;
        }

        // 显示错误信息
        function showError(message) {
            const tbody = document.getElementById('rechargeTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" style="padding: 40px; text-align: center; color: #dc3545; border: 1px solid #e0e0e0;">
                        <div style="margin-bottom: 15px;">${message}</div>
                        <button onclick="searchRechargeRecords(currentPage)" style="background: #007BFF; color: white; border: 1px solid #007BFF;">
                            重试
                        </button>
                    </td>
                </tr>
            `;
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');

            // 设置样式
            let bgColor = '#d1ecf1';
            let borderColor = '#bee5eb';
            let textColor = '#0c5460';

            if (type === 'success') {
                bgColor = '#d4edda';
                borderColor = '#c3e6cb';
                textColor = '#155724';
            } else if (type === 'danger') {
                bgColor = '#f8d7da';
                borderColor = '#f5c6cb';
                textColor = '#721c24';
            } else if (type === 'warning') {
                bgColor = '#fff3cd';
                borderColor = '#ffeaa7';
                textColor = '#856404';
            }

            alertDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                background: ${bgColor};
                border: 1px solid ${borderColor};
                color: ${textColor};
                padding: 15px;
                border-radius: 4px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            `;

            alertDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="background: none; border: none; font-size: 20px; cursor: pointer; color: ${textColor};">&times;</button>
                </div>
            `;

            document.body.appendChild(alertDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>